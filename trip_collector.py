#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HackChat Trip 收集器
用于收集特殊的 trip 值
"""

import json
import time
import random
import websocket
import threading
import string
import re
from datetime import datetime

class TripCollector:
    def __init__(self):
        self.good_trips = []
        self.all_trips = []
        self.running = True
        self.collected_count = 0
        self.good_count = 0
        
        # 创建输出文件
        self.good_trips_file = "good_trips.txt"
        self.all_trips_file = "get_trips.txt"
        
        # 初始化文件
        with open(self.good_trips_file, "w", encoding="utf-8") as f:
            f.write(f"# 好的 Trip 记录 - 开始时间: {datetime.now()}\n")
            f.write("# 格式: 昵称 -> trip (原因)\n\n")
        
        with open(self.all_trips_file, "w", encoding="utf-8") as f:
            f.write(f"# 所有 Trip 记录 - 开始时间: {datetime.now()}\n")
            f.write("# 格式: 昵称 -> trip\n\n")
    
    def generate_random_suffix(self):
        """生成随机的4位后缀"""
        chars = string.digits + string.ascii_uppercase + string.ascii_lowercase
        return ''.join(random.choice(chars) for _ in range(4))
    
    def is_good_trip(self, trip):
        """判断是否是好的 trip"""
        if not trip or len(trip) < 3:
            return False, ""

        trip_upper = trip.upper()

        # 检查连续字符 (AAA, BBB, 111, 222 等) - 最高优先级
        for i in range(len(trip) - 2):
            if trip[i] == trip[i+1] == trip[i+2]:
                return True, f"连续字符: {trip[i]*3}"

        # 检查包含特定单词 - 高优先级
        special_words = ['BOB', 'AI', 'GPT', 'BOT', 'GOD', 'VIP', 'PRO', 'MAX', 'WIN', 'TOP']
        for word in special_words:
            if word in trip_upper:
                return True, f"包含单词: {word}"

        # 检查回文 (ABA, 121, ABCBA 等)
        if len(trip) >= 3 and trip == trip[::-1]:
            return True, "回文"

        # 检查重复模式 (ABAB, 1212 等)
        if len(trip) >= 4:
            half = len(trip) // 2
            if trip[:half] == trip[half:half*2]:
                return True, f"重复模式: {trip[:half]}"

        # 检查连续递增 (ABC, 123, DEF 等) - 但要求至少4位
        if len(trip) >= 4:
            for i in range(len(trip) - 2):
                if (ord(trip[i]) + 1 == ord(trip[i+1]) and
                    ord(trip[i+1]) + 1 == ord(trip[i+2])):
                    return True, f"连续递增: {trip[i:i+3]}"

        # 检查连续递减 (CBA, 321, FED 等) - 但要求至少4位
        if len(trip) >= 4:
            for i in range(len(trip) - 2):
                if (ord(trip[i]) - 1 == ord(trip[i+1]) and
                    ord(trip[i+1]) - 1 == ord(trip[i+2])):
                    return True, f"连续递减: {trip[i:i+3]}"

        # 检查全数字或全字母（长度至少6位，更严格）
        if trip.isdigit() and len(trip) >= 6:
            return True, "全数字"

        if trip.isalpha() and len(trip) >= 6:
            return True, "全字母"

        return False, ""
    
    def save_trip(self, nickname, trip, is_good=False, reason=""):
        """保存 trip 到文件"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if is_good:
            with open(self.good_trips_file, "a", encoding="utf-8") as f:
                f.write(f"{timestamp} | {nickname} -> {trip} ({reason})\n")
            self.good_count += 1
            print(f"🎉 发现好 trip: {nickname} -> {trip} ({reason})")
        
        with open(self.all_trips_file, "a", encoding="utf-8") as f:
            f.write(f"{timestamp} | {nickname} -> {trip}\n")
        
        self.collected_count += 1
    
    def on_message(self, ws, message):
        """处理 WebSocket 消息"""
        try:
            data = json.loads(message)
            
            if data.get("cmd") == "onlineSet":
                # 获取自己的 trip
                users = data.get("users", [])
                for user in users:
                    if user.get("nick", "").startswith("TripBot_"):
                        nickname = user.get("nick", "")
                        trip = user.get("trip", "")
                        
                        if trip:
                            is_good, reason = self.is_good_trip(trip)
                            self.save_trip(nickname, trip, is_good, reason)
                        
                        # 立即离开房间
                        ws.close()
                        return
            
            elif data.get("cmd") == "onlineAdd":
                # 有新用户加入，检查是否是我们自己
                nick = data.get("nick", "")
                trip = data.get("trip", "")
                
                if nick.startswith("TripBot_") and trip:
                    is_good, reason = self.is_good_trip(trip)
                    self.save_trip(nick, trip, is_good, reason)
                    
                    # 立即离开房间
                    ws.close()
                    return
                    
        except json.JSONDecodeError:
            pass
        except Exception as e:
            print(f"处理消息错误: {e}")
    
    def on_error(self, ws, error):
        """处理 WebSocket 错误"""
        pass  # 忽略错误，继续下一次尝试
    
    def on_close(self, ws, close_status_code=None, close_msg=None):
        """WebSocket 连接关闭"""
        pass  # 正常关闭，准备下一次连接
    
    def on_open(self, ws):
        """WebSocket 连接打开"""
        # 生成随机昵称
        suffix = self.generate_random_suffix()
        nickname = f"TripBot_{suffix}"
        
        # 加入房间
        join_data = {
            "cmd": "join",
            "channel": "test",  # 使用测试房间，避免打扰其他用户
            "nick": nickname
        }
        ws.send(json.dumps(join_data))
    
    def collect_single_trip(self):
        """收集单个 trip"""
        try:
            ws = websocket.WebSocketApp(
                "wss://icantofun.click/",
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close,
                on_open=self.on_open
            )
            
            # 设置超时，避免卡住
            ws.run_forever(ping_interval=5, ping_timeout=3)
            
        except Exception as e:
            print(f"连接错误: {e}")
    
    def start_collecting(self, max_attempts=1000):
        """开始收集 trip"""
        print(f"开始收集 trip，目标: {max_attempts} 次尝试")
        print(f"好 trip 将保存到: {self.good_trips_file}")
        print(f"所有 trip 将保存到: {self.all_trips_file}")
        print("按 Ctrl+C 停止收集\n")
        
        try:
            for i in range(max_attempts):
                if not self.running:
                    break
                
                print(f"尝试 {i+1}/{max_attempts} - 已收集: {self.collected_count}, 好 trip: {self.good_count}")
                
                self.collect_single_trip()
                
                # 短暂延迟，避免请求过快
                time.sleep(0.5)
                
        except KeyboardInterrupt:
            print("\n收集已停止")
        
        print(f"\n收集完成！")
        print(f"总共收集: {self.collected_count} 个 trip")
        print(f"好 trip: {self.good_count} 个")
        print(f"好 trip 比例: {self.good_count/max(self.collected_count, 1)*100:.2f}%")

if __name__ == "__main__":
    print("=" * 50)
    print("HackChat Trip 收集器")
    print("=" * 50)
    print("这个脚本会自动连接 HackChat 并收集 trip 值")
    print("好的 trip 标准:")
    print("- 连续字符 (AAA, BBB, 111, 222)")
    print("- 连续递增/递减 (ABC, 123, CBA, 321)")
    print("- 包含特殊单词 (BOB, AI, GPT, BOT 等)")
    print("- 回文 (ABA, 121)")
    print("- 重复模式 (ABAB, 1212)")
    print("- 全数字/全字母 (12345, ABCDE)")
    print()

    # 询问用户要收集多少次
    try:
        max_attempts = input("请输入要尝试的次数 (默认1000): ").strip()
        if not max_attempts:
            max_attempts = 1000
        else:
            max_attempts = int(max_attempts)
    except ValueError:
        max_attempts = 1000

    collector = TripCollector()

    try:
        collector.start_collecting(max_attempts)
    except KeyboardInterrupt:
        collector.running = False
        print("\n程序已停止")
