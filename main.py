import json
import time
import random
import subprocess
import websocket
import threading
import os
import string
from modules.uno_game import UnoGame
from modules.wordle_game import WordleGame
from modules.wordle_image import generate_wordle_image

from modules.html_generator import process_history_request
from modules.user_system import initialize_user_system

def generate_random_nickname():
    """生成随机昵称 BoB_xxxx，其中 xxxx 是数字和大写字母的随机组合"""
    # 数字和大写字母的字符集
    chars = string.digits + string.ascii_uppercase
    # 生成4位随机字符
    random_suffix = ''.join(random.choice(chars) for _ in range(4))
    return f"BoB_{random_suffix}"

def log_message(msg_type, content, nick=None):
    """统一的日志输出函数"""
    timestamp = time.strftime("%H:%M:%S")

    if msg_type == "chat":
        print(f"[{timestamp}] 💬 {nick}: {content}")
    elif msg_type == "join":
        print(f"[{timestamp}] ➕ {nick} 加入房间")
    elif msg_type == "leave":
        print(f"[{timestamp}] ➖ {nick} 离开房间")
    elif msg_type == "whisper":
        print(f"[{timestamp}] 🔒 私聊: {content}")
    elif msg_type == "bot_send":
        print(f"[{timestamp}] 🤖 BoB: {content}")
    elif msg_type == "error":
        print(f"[{timestamp}] ❌ 错误: {content}")
    elif msg_type == "info":
        print(f"[{timestamp}] ℹ️  {content}")
    elif msg_type == "game":
        print(f"[{timestamp}] 🎮 游戏: {content}")
    else:
        print(f"[{timestamp}] 📝 {msg_type}: {content}")

def sendd(text, customId=None):
    message = {"cmd": "chat", "text": str(text)}
    if customId:
        message["customId"] = customId
    return json.dumps(message)

def update(text, customId):
    return json.dumps({"cmd": "updateMessage", "text": str(text), "mode": "overwrite", "customId": customId})



# 全局游戏实例
uno_game = UnoGame()
wordle_game = WordleGame()

# 全局WebSocket实例
ws = None

# 默认重连配置
DEFAULT_RECONNECT_CONFIG = {
    "max_retries": -1,  # -1 表示无限重试
    "initial_delay": 5,  # 初始重连延迟（秒）
    "max_delay": 60,     # 最大重连延迟（秒）
    "backoff_factor": 1.5,  # 退避因子
    "enable_reconnect": True  # 是否启用重连
}

# 重连配置（将从文件加载）
RECONNECT_CONFIG = DEFAULT_RECONNECT_CONFIG.copy()

# 重连状态
reconnect_state = {
    "is_running": True,
    "retry_count": 0,
    "current_delay": RECONNECT_CONFIG["initial_delay"]
}

def load_welcome_messages():
    """加载欢迎语配置"""
    try:
        if os.path.exists("config/welcome_messages.json"):
            with open("config/welcome_messages.json", "r", encoding="utf-8") as f:
                return json.load(f)
        else:
            # 如果文件不存在，返回默认配置
            default_config = {
                "welcome_messages": {
                    "prefixes": ["你好鸭~", "欢迎欢迎~", "哇！", "嗨~"],
                    "suffixes": ["今天也要开开心心的过啊！", "OvO！", "今天也是幸运的一天！"],
                    "standalone": ["你好鸭~今天也要开开心心的过啊！", "欢迎来到这里！OvO！"],
                    "with_nickname": ["{nick}来啦~欢迎欢迎！", "哇！{nick}！今天也要开开心心的过啊！"]
                },
                "settings": {
                    "use_nickname_probability": 0.4,
                    "use_standalone_probability": 0.3,
                    "use_prefix_suffix_probability": 0.3,
                    "enable_random_welcome": True
                }
            }
            return default_config
    except Exception as e:
        log_message("error", f"加载欢迎语配置失败: {e}")
        return None

def load_reconnect_config():
    """加载重连配置"""
    global RECONNECT_CONFIG, reconnect_state

    try:
        if os.path.exists("config/reconnect_config.json"):
            with open("config/reconnect_config.json", "r", encoding="utf-8") as f:
                config_data = json.load(f)
                if "reconnect_settings" in config_data:
                    # 更新配置，保留默认值作为备用
                    for key, value in config_data["reconnect_settings"].items():
                        if key in DEFAULT_RECONNECT_CONFIG:
                            RECONNECT_CONFIG[key] = value

                    # 更新重连状态的初始延迟
                    reconnect_state["current_delay"] = RECONNECT_CONFIG["initial_delay"]

                    log_message("info", "重连配置加载成功")
                    return True

        log_message("info", "使用默认重连配置")
        return False

    except Exception as e:
        log_message("error", f"加载重连配置失败: {e}，使用默认配置")
        return False

def get_random_welcome_message(nickname=None):
    """获取随机欢迎语"""
    config = load_welcome_messages()
    if not config or not config["settings"]["enable_random_welcome"]:
        return f"你好鸭~{nickname}！" if nickname else "你好鸭~"

    messages = config["welcome_messages"]
    settings = config["settings"]

    # 根据概率决定使用哪种类型的欢迎语
    rand = random.random()

    if nickname and rand < settings["use_nickname_probability"]:
        # 使用带昵称的欢迎语
        if messages["with_nickname"]:
            template = random.choice(messages["with_nickname"])
            return template.format(nick=nickname)

    elif rand < settings["use_nickname_probability"] + settings["use_standalone_probability"]:
        # 使用独立的欢迎语
        if messages["standalone"]:
            return random.choice(messages["standalone"])

    else:
        # 使用前缀+后缀组合
        if messages["prefixes"] and messages["suffixes"]:
            prefix = random.choice(messages["prefixes"])
            suffix = random.choice(messages["suffixes"])
            if nickname:
                return f"{prefix}{nickname}！{suffix}"
            else:
                return f"{prefix}{suffix}"

    # 如果所有条件都不满足，返回默认欢迎语
    return f"你好鸭~{nickname}！" if nickname else "你好鸭~"

def calculate_reconnect_delay():
    """计算重连延迟时间（指数退避算法）"""
    global reconnect_state

    if reconnect_state["retry_count"] == 0:
        delay = RECONNECT_CONFIG["initial_delay"]
    else:
        delay = min(
            reconnect_state["current_delay"] * RECONNECT_CONFIG["backoff_factor"],
            RECONNECT_CONFIG["max_delay"]
        )

    reconnect_state["current_delay"] = delay
    return delay

def reset_reconnect_state():
    """重置重连状态（连接成功时调用）"""
    global reconnect_state
    reconnect_state["retry_count"] = 0
    reconnect_state["current_delay"] = RECONNECT_CONFIG["initial_delay"]

def should_reconnect():
    """判断是否应该重连"""
    global reconnect_state

    if not RECONNECT_CONFIG["enable_reconnect"]:
        return False

    if not reconnect_state["is_running"]:
        return False

    max_retries = RECONNECT_CONFIG["max_retries"]
    if max_retries > 0 and reconnect_state["retry_count"] >= max_retries:
        log_message("error", f"已达到最大重试次数 {max_retries}，停止重连")
        return False

    return True

def start_websocket_connection():
    """启动WebSocket连接"""
    global ws, reconnect_state

    try:
        # 生成随机昵称
        random_nickname = generate_random_nickname()
        log_message("info", f"使用昵称: {random_nickname}")

        # 创建主实例
        main_instance = main(room="lounges", name=random_nickname)

        # 关闭 WebSocket 调试输出
        websocket.enableTrace(False)

        if reconnect_state["retry_count"] == 0:
            log_message("info", "正在连接到 HackChat...")
        else:
            log_message("info", f"正在重连到 HackChat... (第{reconnect_state['retry_count']}次)")

        # 创建WebSocket应用
        ws = websocket.WebSocketApp(
            "wss://icantofun.click/",
            on_message=main_instance.on_message,
            on_error=main_instance.on_error,
            on_close=main_instance.on_close,
            on_open=main_instance.on_open
        )

        # 启动连接（这会阻塞直到连接关闭）
        ws.run_forever()

    except Exception as e:
        log_message("error", f"WebSocket连接失败: {e}")

        # 如果连接失败且应该重连，则安排重连
        if should_reconnect():
            schedule_reconnect()

def schedule_reconnect():
    """安排重连"""
    global reconnect_state

    if not should_reconnect():
        return

    reconnect_state["retry_count"] += 1
    delay = calculate_reconnect_delay()

    log_message("info", f"第 {reconnect_state['retry_count']} 次重连尝试，{delay}秒后开始...")

    # 直接在当前线程中等待并重连，不使用新线程
    time.sleep(delay)
    if should_reconnect():
        log_message("info", "开始重连...")
        start_websocket_connection()

def initialize_directories_and_files():
    """初始化必要的目录和文件"""
    # 创建必要的目录
    directories = ['history', 'userdata', 'temp']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        log_message("info", f"确保目录存在: {directory}")

    # 创建必要的文件
    files_to_create = {
        'config/messages.json': '{}'
    }

    for filename, default_content in files_to_create.items():
        if not os.path.exists(filename):
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(default_content)
                log_message("info", f"创建文件: {filename}")
            except Exception as e:
                log_message("error", f"创建文件失败 {filename}: {e}")
        else:
            log_message("info", f"文件已存在: {filename}")

    log_message("info", "初始化完成！")



class runbox:
    def __init__(self, room, name):
        self.room = room
        self.name = name
        self.online_users = []
        self.game_on = False
        self.secret_number = None
        self.ad_users = []

    def handle(self, json_data):
        self.json_data = json_data
        if "cmd" in self.json_data:
            if self.json_data["cmd"] == "chat":
                # 记录聊天消息
                nick = self.json_data.get("nick", "unknown")
                text = self.json_data.get("text", "")
                log_message("chat", text, nick)
                self.chat()
            elif self.json_data["cmd"] == "info":
                # 记录系统信息
                info_text = self.json_data.get("text", "")
                if "whispered" in info_text:
                    log_message("whisper", info_text)
                else:
                    log_message("info", info_text)
                self.info()
            elif self.json_data["cmd"] == "onlineAdd":
                # 记录用户加入
                self.nick = self.json_data['nick']
                log_message("join", f"用户加入房间", self.nick)
                self.onlineadd()
            elif self.json_data["cmd"] == "onlineRemove":
                # 记录用户离开
                nick = self.json_data.get('nick', 'unknown')
                log_message("leave", f"用户离开房间", nick)
            else:
                log_message("error", f"未知命令: {self.json_data.get('cmd', 'unknown')}")

    def handle_command(self, command_type, username, get, custom_id):
        try:
            # 使用统一的 AI 处理脚本
            output = subprocess.check_output(['python', 'modules/ai.py', command_type, username, get], universal_newlines=True)
        except subprocess.CalledProcessError as e:
            output = f"命令执行失败: {str(e)}"
        except Exception as e:
            output = f"未知错误: {str(e)}"

        response = username + "，" + output
        log_message("bot_send", response)
        ws.send(update(response, custom_id))

    def chat(self):
        # 记录聊天历史 - 改进版
        self.log_chat_message()

    def log_chat_message(self):
        """改进的聊天记录功能"""
        try:
            # 确保 history 目录存在
            history_dir = "history"
            os.makedirs(history_dir, exist_ok=True)

            # 使用更安全的文件名格式
            date_str = time.strftime("%Y-%m-%d")
            room_safe = self.room.replace(" ", "_").replace("/", "_")
            filename = f"{date_str}_{room_safe}.jsonl"
            filepath = os.path.join(history_dir, filename)

            # 创建结构化的消息记录
            message_record = {
                "timestamp": time.time(),
                "datetime": time.strftime("%Y-%m-%d %H:%M:%S"),
                "room": self.room,
                "nick": self.json_data.get("nick", "unknown"),
                "trip": self.json_data.get("trip", ""),
                "text": self.json_data.get("text", ""),
                "cmd": self.json_data.get("cmd", "chat")
            }

            # 使用UTF-8编码安全写入（跨平台兼容）
            with open(filepath, "a", encoding="utf-8") as fp:
                try:
                    # 尝试使用文件锁（Unix/Linux）
                    import fcntl
                    fcntl.flock(fp.fileno(), fcntl.LOCK_EX)
                    fp.write(json.dumps(message_record, ensure_ascii=False) + "\n")
                    fcntl.flock(fp.fileno(), fcntl.LOCK_UN)
                except ImportError:
                    # Windows 系统不支持 fcntl，直接写入
                    fp.write(json.dumps(message_record, ensure_ascii=False) + "\n")

        except Exception as e:
            print(f"记录聊天历史失败: {e}")  # 记录错误但不影响主功能

        # 定义命令配置
        commands = {
            "@BoB": {"script": "ai", "prefix_len": 5, "message": "正在分析并生成回答，请稍后..."},
            "draw": {"script": "draw", "prefix_len": 5, "message": "正在使用 Flux 模型绘画，请稍后..."}
        }

        # 检查是否是机器人自己的消息（昵称以 BoB 开头）
        def is_bot_message(nick):
            return nick.startswith("BoB")

        # 处理留言命令
        if self.json_data["text"].startswith("msg "):
            parts = self.json_data["text"].split(" ", 2)
            if len(parts) >= 3:
                to_user = parts[1]
                message = parts[2]
                self.leave_message(to_user, message)
                return

        # 处理 UNO 游戏命令
        if self.json_data["text"].startswith("uno "):
            self.handle_uno_command()
            return

        # 处理 Wordle 游戏命令
        if self.json_data["text"].startswith("wordle "):
            self.handle_wordle_command()
            return

        # 处理 Wordle 猜测（任何5字母单词）
        text = self.json_data["text"].strip()
        if len(text) == 5 and text.isalpha() and wordle_game.game_active:
            self.handle_wordle_guess(text)
            return

        # 统一处理命令
        for command, config in commands.items():
            # 检查命令匹配和确定实际前缀长度
            command_matches = False
            actual_prefix_len = config["prefix_len"]

            if command == "@BoB":
                # @BoB 命令特殊处理：支持 @BoB 或 @机器人实际昵称
                if self.json_data["text"].startswith("@BoB "):  # 确保后面有空格
                    command_matches = True
                    actual_prefix_len = 5  # "@BoB " 的长度
                elif self.json_data["text"].startswith(f"@{self.name} "):  # 确保后面有空格
                    command_matches = True
                    actual_prefix_len = len(f"@{self.name} ")  # "@BoB_xxxx " 的长度
            else:
                command_matches = self.json_data["text"].startswith(command)

            if command_matches and not is_bot_message(self.json_data["nick"]):
                # @BoB 命令需要额外检查
                if command == "@BoB" and self.json_data["trip"] == "2UvIfa":
                    continue

                username = self.json_data["nick"]
                get = self.json_data["text"][actual_prefix_len:]
                custom_id = str(random.randint(100000, 999999))
                ws.send(sendd(username + "，" + config["message"], custom_id))

                # 创建新线程处理命令
                thread = threading.Thread(target=self.handle_command, args=(config["script"], username, get, custom_id))
                thread.start()
                return  # 找到匹配的命令后直接返回

        if "delete" == self.json_data["text"]:
            # 确保 userdata 目录存在
            userdata_dir = "userdata"
            os.makedirs(userdata_dir, exist_ok=True)

            filename = self.json_data["nick"] + "_chat_history.json"
            filepath = os.path.join(userdata_dir, filename)
            if os.path.exists(filepath):
                os.remove(filepath)
                ws.send(sendd("聊天记录已删除"))
            else:
                ws.send(sendd("找不到聊天记录文件"))

        # history command - 改进版
        elif "history" in self.json_data["text"]:
            self.show_chat_history()

        elif self.json_data["text"].startswith("help"):
            self.handle_help_command()

    def show_chat_history(self):
        """改进的历史记录显示功能"""
        try:
            args = self.json_data["text"].split()
            # 解析请求的消息数量
            if len(args) > 1 and args[1].isdigit():
                requested_count = int(args[1])
                requested_count = min(requested_count, 500)  # 限制最大数量
            else:
                requested_count = None  # 使用默认数量

            # 确保 history 目录存在
            history_dir = "history"
            os.makedirs(history_dir, exist_ok=True)

            date_str = time.strftime("%Y-%m-%d")
            room_safe = self.room.replace(" ", "_").replace("/", "_")
            filename = f"{date_str}_{room_safe}.jsonl"
            filepath = os.path.join(history_dir, filename)

            messages = []

            # 读取 JSON Lines 格式的历史记录
            try:
                with open(filepath, "r", encoding="utf-8") as fp:
                    for line in fp:
                        if line.strip():
                            try:
                                msg = json.loads(line.strip())
                                # 只显示聊天消息，过滤系统消息
                                if msg.get("cmd") == "chat" and msg.get("text"):
                                    messages.append(msg)
                            except json.JSONDecodeError:
                                continue
            except FileNotFoundError:
                pass

            if not messages:
                ws.send(sendd("/w " + self.json_data["nick"] + " 今天还没有聊天记录"))
                return

            # 格式化消息用于 HTML 显示
            formatted_messages = []
            for msg in messages:
                formatted_msg = {
                    "time": msg.get("datetime", "").split()[1] if " " in msg.get("datetime", "") else "",
                    "nick": msg.get("nick", "unknown"),
                    "text": msg.get("text", "")
                }
                formatted_messages.append(formatted_msg)

            # 使用 HTML 生成器处理请求
            result_type, content, message_count = process_history_request(
                formatted_messages, self.room, date_str, requested_count
            )

            if result_type == "file":
                # HTML 文件上传成功
                ws.send(sendd(f"/w {self.json_data['nick']} 📄 历史记录已生成 HTML 文件: {content}\n包含最近 {message_count} 条消息"))
            elif result_type == "text":
                # 直接文本显示
                ws.send(sendd(f"/w {self.json_data['nick']} 最近 {message_count} 条消息:\n{content}"))
            elif result_type == "text_fallback":
                # 上传失败，分块发送
                max_length = 1000
                chunks = [content[i:i+max_length] for i in range(0, len(content), max_length)]
                for i, chunk in enumerate(chunks):
                    ws.send(sendd(f"/w {self.json_data['nick']} 历史记录 ({i+1}/{len(chunks)}):\n{chunk}"))
                ws.send(sendd(f"/w {self.json_data['nick']} ⚠️ HTML 文件上传失败，已分块显示"))

        except Exception as e:
            ws.send(sendd(f"/w {self.json_data['nick']} 获取历史记录失败: {str(e)}"))




    def info(self):
        if "whispered" in self.json_data["text"]:
            username = self.json_data["from"]
            parts = self.json_data["text"].split("whispered:", 1)
            if len(parts) > 1:
                get = parts[1].strip()
                try:
                    output = subprocess.check_output(['python', 'ai.py', 'ai', username, get], universal_newlines=True)
                    ws.send(sendd(f'/w {username} ' + output))
                except subprocess.CalledProcessError as e:
                    ws.send(sendd(f'/w {username} AI 执行出错: {str(e)}'))
                except Exception as e:
                    ws.send(sendd(f'/w {username} 未知错误: {str(e)}'))



    def onlineadd(self):
        from modules.user_system import UserSystem

        # 创建用户系统实例，使用config目录下的配置文件
        user_system = UserSystem("config/user_config.json")

        # 获取当前进入房间的用户的昵称
        user = self.nick

        # 检查用户是否有留言
        self.check_messages(user)

        # 检查用户是否已知
        if user_system.is_known_user(user):
            # 老用户 - 使用随机欢迎语
            returning_message = user_system.get_returning_message()
            random_welcome = get_random_welcome_message(user)
            response = f"{random_welcome} {returning_message}"
            log_message("bot_send", response)
            ws.send(sendd(response))
        else:
            # 新用户
            welcome_msg = user_system.get_welcome_message()
            whisper_msg = f"/w {user} {welcome_msg}"

            # 使用随机欢迎语替代固定的"你好！{user}"
            greeting_msg = get_random_welcome_message(user)

            log_message("bot_send", f"私聊新用户: {welcome_msg}")
            log_message("bot_send", greeting_msg)

            ws.send(sendd(whisper_msg))
            ws.send(sendd(greeting_msg))

            # 添加到已知用户列表
            user_system.add_user(user)





    def leave_message(self, to_user, message):
        """留言功能 - JSON 版本"""
        try:
            # 留言文件路径
            message_file = "config/messages.json"

            # 加载现有留言
            messages_data = {}
            if os.path.exists(message_file):
                try:
                    with open(message_file, "r", encoding="utf-8") as f:
                        messages_data = json.load(f)
                except json.JSONDecodeError:
                    # 文件损坏，创建新文件
                    messages_data = {}

            # 确保用户的留言列表存在
            if to_user not in messages_data:
                messages_data[to_user] = []

            # 创建留言记录
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            new_message = {
                "timestamp": timestamp,
                "from": self.json_data.get('nick', 'unknown'),
                "message": message
            }

            # 添加新留言
            messages_data[to_user].append(new_message)

            # 保存留言
            with open(message_file, "w", encoding="utf-8") as f:
                json.dump(messages_data, f, ensure_ascii=False, indent=2)

            ws.send(sendd(f"已给 {to_user} 留言成功！当 {to_user} 进入房间时会收到您的消息。"))

        except Exception as e:
            ws.send(sendd(f"留言失败: {str(e)}"))

    def check_messages(self, username):
        """检查用户是否有留言 - JSON 版本"""
        try:
            message_file = "config/messages.json"
            if not os.path.exists(message_file):
                return

            # 加载留言数据
            with open(message_file, "r", encoding="utf-8") as f:
                try:
                    messages_data = json.load(f)
                except json.JSONDecodeError:
                    return

            # 检查用户是否有留言
            if username in messages_data and messages_data[username]:
                # 格式化留言
                formatted_messages = []
                for msg in messages_data[username]:
                    formatted_messages.append(
                        f"[{msg['timestamp']}] {msg['from']}: {msg['message']}"
                    )

                # 发送留言给用户
                message_text = "\n".join(formatted_messages)
                ws.send(sendd(f"/w {username} 您有新留言:\n{message_text}"))

                # 删除已读留言
                messages_data.pop(username)

                # 保存更新后的留言数据
                with open(message_file, "w", encoding="utf-8") as f:
                    json.dump(messages_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"检查留言失败: {e}")

    def handle_uno_command(self):
        """处理 UNO 游戏命令"""
        global uno_game

        text = self.json_data["text"]
        nick = self.json_data["nick"]

        parts = text.split()
        if len(parts) < 2:
            ws.send(sendd("UNO命令格式错误"))
            return

        command = parts[1]

        if command == "join":
            # 加入游戏
            if uno_game.add_player(nick):
                ws.send(sendd(f"{nick} 加入了UNO游戏！当前玩家: {len(uno_game.players)}人"))
            else:
                ws.send(sendd("加入失败，可能已在游戏中或游戏已满"))

        elif command == "leave":
            # 离开游戏
            if uno_game.remove_player(nick):
                ws.send(sendd(f"{nick} 离开了UNO游戏"))
                if uno_game.game_started and len(uno_game.players) < 2:
                    uno_game = UnoGame()  # 重置游戏
                    ws.send(sendd("人数不足，游戏重置"))
            else:
                ws.send(sendd("你不在游戏中"))

        elif command == "start":
            # 开始游戏
            if uno_game.start_game():
                ws.send(sendd("🎮 UNO游戏开始！"))
                ws.send(sendd(uno_game.get_game_status()))
                # 私聊发送每个玩家的手牌
                for player in uno_game.players:
                    hand = uno_game.get_player_hand(player)
                    ws.send(sendd(f"/w {player} 你的手牌: {' '.join(hand)}"))
            else:
                ws.send(sendd("开始失败，需要至少2个玩家"))

        elif command == "status":
            # 查看游戏状态
            ws.send(sendd(uno_game.get_game_status()))

        elif command == "hand":
            # 查看手牌
            if nick in uno_game.players:
                hand = uno_game.get_player_hand(nick)
                ws.send(sendd(f"/w {nick} 你的手牌: {' '.join(hand)}"))
            else:
                ws.send(sendd("你不在游戏中"))

        elif command == "play":
            # 出牌
            if len(parts) < 3:
                ws.send(sendd("请指定要出的牌，格式: uno play 红5"))
                return

            card_str = parts[2]
            new_color = parts[3] if len(parts) > 3 else None

            success, message = uno_game.play_card(nick, card_str, new_color)
            ws.send(sendd(f"{nick}: {message}"))

            if success and not uno_game.winner:
                # 显示游戏状态
                ws.send(sendd(uno_game.get_game_status()))
                # 私聊发送当前玩家手牌
                current_player = uno_game.get_current_player()
                if current_player:
                    hand = uno_game.get_player_hand(current_player)
                    ws.send(sendd(f"/w {current_player} 轮到你了！手牌: {' '.join(hand)}"))

        elif command == "draw":
            # 摸牌
            if nick == uno_game.get_current_player():
                drawn_cards = uno_game.draw_card(nick, 1)
                if drawn_cards:
                    ws.send(sendd(f"{nick} 摸了1张牌"))
                    uno_game.next_turn()
                    ws.send(sendd(uno_game.get_game_status()))
                    # 私聊发送新手牌
                    hand = uno_game.get_player_hand(nick)
                    ws.send(sendd(f"/w {nick} 你的手牌: {' '.join(hand)}"))
                else:
                    ws.send(sendd("没有牌可摸了"))
            else:
                ws.send(sendd("不是你的回合"))

        elif command == "uno":
            # 喊UNO
            success, message = uno_game.call_uno(nick)
            ws.send(sendd(f"{nick}: {message}"))

        elif command == "reset":
            # 重置游戏
            uno_game = UnoGame()
            ws.send(sendd("UNO游戏已重置"))

        else:
            ws.send(sendd("未知的UNO命令"))

    def handle_wordle_command(self):
        """处理 Wordle 游戏命令"""
        global wordle_game

        text = self.json_data["text"]
        nick = self.json_data["nick"]

        parts = text.split()
        if len(parts) < 2:
            ws.send(sendd("Wordle命令格式错误"))
            return

        command = parts[1]

        if command == "start":
            # 开始新游戏
            target = wordle_game.start_new_game()
            ws.send(sendd("🎯 Wordle 游戏开始！猜一个5字母英文单词"))

            # 生成并显示游戏图片
            self.show_wordle_image()

            ws.send(sendd(wordle_game.get_game_status()))
            print(f"Wordle答案: {target}")  # 服务器日志

        elif command == "status":
            # 查看游戏状态
            ws.send(sendd(wordle_game.get_game_status()))
            if wordle_game.game_active or len(wordle_game.guesses) > 0:
                self.show_wordle_image()

        elif command == "help":
            # 重定向到统一帮助系统
            ws.send(sendd("请使用 'help wordle' 查看详细的Wordle游戏帮助"))

        else:
            ws.send(sendd("未知的Wordle命令，输入 'wordle help' 查看帮助"))

    def handle_wordle_guess(self, word):
        """处理 Wordle 猜测"""
        global wordle_game

        nick = self.json_data["nick"]
        success, message = wordle_game.make_guess(word)

        if success:
            ws.send(sendd(f"{nick} 猜测: {word.upper()}"))

            # 显示游戏图片
            self.show_wordle_image()

            ws.send(sendd(wordle_game.get_game_status()))

            if not wordle_game.game_active:
                # 游戏结束，可以开始新游戏
                ws.send(sendd("输入 'wordle start' 开始新游戏"))
        else:
            ws.send(sendd(f"{nick}: {message}"))

    def show_wordle_image(self):
        """显示 Wordle 游戏图片"""
        try:
            # 生成图片并上传
            image_url, local_path = generate_wordle_image(wordle_game)

            if image_url:
                # 使用 Markdown 格式显示图片
                ws.send(sendd(f"![Wordle Game]({image_url})"))
            else:
                # 如果上传失败，显示本地路径信息
                if local_path:
                    ws.send(sendd(f"📷 图片已生成: {local_path} (上传失败，请检查网络)"))
                else:
                    # 回退到文本显示
                    ws.send(sendd(wordle_game.get_game_display()))

        except Exception as e:
            print(f"显示 Wordle 图片失败: {e}")
            # 回退到文本显示
            ws.send(sendd(wordle_game.get_game_display()))

    def handle_help_command(self):
        """处理帮助命令"""
        text = self.json_data["text"].strip()
        nick = self.json_data["nick"]

        # 解析帮助命令
        parts = text.split()
        if len(parts) == 1:
            # 显示主帮助
            help_topic = "main"
        else:
            # 显示特定功能帮助
            help_topic = parts[1].lower()

        # 加载帮助信息
        try:
            with open('config/help.json', 'r', encoding='utf-8') as f:
                help_data = json.load(f)
        except FileNotFoundError:
            ws.send(sendd("/w " + nick + " 帮助文件未找到"))
            return
        except json.JSONDecodeError:
            ws.send(sendd("/w " + nick + " 帮助文件格式错误"))
            return

        # 检查帮助主题是否存在
        if help_topic not in help_data:
            available_topics = list(help_data.keys())
            available_topics.remove("main")  # 移除main，因为它是默认的
            ws.send(sendd(f"/w {nick} 未知的帮助主题。可用主题: {', '.join(available_topics)}"))
            return

        # 格式化帮助信息
        help_info = help_data[help_topic]
        help_text = help_info["title"] + "\n\n"

        for section_title, section_content in help_info["sections"].items():
            help_text += section_title + ":\n"
            for item in section_content:
                help_text += "  " + item + "\n"
            help_text += "\n"

        # 如果是主帮助，添加详细帮助提示
        if help_topic == "main":
            help_text += "💡 输入 'help <功能名>' 查看详细帮助\n"
            help_text += "   例如: help uno, help wordle, help ai"

        # 发送帮助信息
        ws.send(sendd("/w " + nick + " " + help_text.strip()))



class main:
    def __init__(self,room,name):
        self.runbox = runbox(room,name)
        self.room = self.runbox.room
        self.name = self.runbox.name

    def on_message(self, ws, message):
        try:
            js_ms = json.loads(message)
            self.runbox.handle(js_ms)
        except json.JSONDecodeError as e:
            log_message("error", f"JSON解析失败: {e}")
        except Exception as e:
            log_message("error", f"消息处理失败: {e}")

    def on_error(self, ws, error):
        log_message("error", f"WebSocket错误: {error}")
        # 错误时不立即重连，等待 on_close 处理

    def on_close(self, ws, close_status_code=None, close_msg=None):
        if close_status_code:
            log_message("info", f"WebSocket连接关闭 - 状态码: {close_status_code}, 消息: {close_msg}")
        else:
            log_message("info", "WebSocket连接已关闭")

        # 如果应该重连，则安排重连
        if should_reconnect():
            log_message("info", "检测到连接断开，准备重连...")
            schedule_reconnect()
        else:
            log_message("info", "机器人已退出")

    def on_open(self, ws):
        log_message("info", "WebSocket 连接已建立")

        # 检查是否是第一次连接（非重连）
        is_first_connection = reconnect_state["retry_count"] == 0

        # 重置重连状态
        reset_reconnect_state()

        # 初始化机器人
        try:
            ws.send(json.dumps({"cmd": "join", "channel": str(self.room), "nick": str(self.name)}))
            time.sleep(1)
            ws.send(sendd("/color #F5DEB3"))

            # 只在第一次连接时发送欢迎消息
            if is_first_connection:
                time.sleep(1)
                ws.send(sendd("Hi，我是BoB"))
                log_message("info", f"机器人已加入房间，昵称: {self.name}")
            else:
                log_message("info", f"机器人已静默重连，昵称: {self.name}")

        except Exception as e:
            log_message("error", f"机器人初始化失败: {e}")

def stop_reconnect():
    """停止重连（用于优雅退出）"""
    global reconnect_state
    reconnect_state["is_running"] = False
    log_message("info", "重连已停止")

if __name__ == "__main__":
    try:
        # 初始化目录和文件
        log_message("info", "HackChat BoB 机器人启动中...")
        initialize_directories_and_files()

        # 初始化用户系统
        log_message("info", "初始化用户系统...")
        initialize_user_system()

        # 加载重连配置
        load_reconnect_config()

        log_message("info", f"重连配置: 启用={RECONNECT_CONFIG['enable_reconnect']}, 最大重试={RECONNECT_CONFIG['max_retries']}, 初始延迟={RECONNECT_CONFIG['initial_delay']}秒")

        # 启动WebSocket连接（包含重连机制）
        # 这个函数会一直运行，直到手动停止或达到最大重试次数
        start_websocket_connection()

    except KeyboardInterrupt:
        log_message("info", "收到中断信号，正在退出...")
        stop_reconnect()
    except Exception as e:
        log_message("error", f"程序异常退出: {e}")
        stop_reconnect()
    finally:
        log_message("info", "机器人已退出")