import random
import time
import os

class WordleGame:
    """Wordle 游戏类"""
    
    def __init__(self):
        self.target_word = ""
        self.guesses = []
        self.guessed_words = set()  # 记录已猜测的单词
        self.max_guesses = 6
        self.word_length = 5
        self.game_active = False
        self.start_time = None
        self.answer_words = self.load_answer_words()  # 题库（答案词汇）
        self.valid_words = self.load_valid_words()    # 有效猜测词汇
        
        # 常用5字母英文单词列表（作为备选答案）
        self.word_list = [
            "ABOUT", "ABOVE", "ABUSE", "ACTOR", "ACUTE", "ADMIT", "ADOPT", "ADULT", "AFTER", "AGAIN",
            "AGENT", "AGREE", "AHEAD", "ALARM", "ALBUM", "ALERT", "ALIEN", "ALIGN", "ALIKE", "ALIVE",
            "ALLOW", "ALONE", "ALONG", "ALTER", "ANGEL", "ANGER", "ANGLE", "ANGRY", "APART", "APPLE",
            "APPLY", "ARENA", "ARGUE", "ARISE", "ARRAY", "ASIDE", "ASSET", "AVOID", "AWAKE", "AWARD",
            "AWARE", "BADLY", "BAKER", "BASES", "BASIC", "BEACH", "BEGAN", "BEGIN", "BEING", "BELOW",
            "BENCH", "BILLY", "BIRTH", "BLACK", "BLAME", "BLANK", "BLIND", "BLOCK", "BLOOD", "BOARD",
            "BOOST", "BOOTH", "BOUND", "BRAIN", "BRAND", "BRAVE", "BREAD", "BREAK", "BREED", "BRIEF",
            "BRING", "BROAD", "BROKE", "BROWN", "BUILD", "BUILT", "BUYER", "CABLE", "CALIF", "CARRY",
            "CATCH", "CAUSE", "CHAIN", "CHAIR", "CHAOS", "CHARM", "CHART", "CHASE", "CHEAP", "CHECK",
            "CHEST", "CHIEF", "CHILD", "CHINA", "CHOSE", "CIVIL", "CLAIM", "CLASS", "CLEAN", "CLEAR",
            "CLICK", "CLIMB", "CLOCK", "CLOSE", "CLOUD", "COACH", "COAST", "COULD", "COUNT", "COURT",
            "COVER", "CRAFT", "CRASH", "CRAZY", "CREAM", "CRIME", "CROSS", "CROWD", "CROWN", "CRUDE",
            "CURVE", "CYCLE", "DAILY", "DANCE", "DATED", "DEALT", "DEATH", "DEBUT", "DELAY", "DEPTH",
            "DOING", "DOUBT", "DOZEN", "DRAFT", "DRAMA", "DRANK", "DREAM", "DRESS", "DRILL", "DRINK",
            "DRIVE", "DROVE", "DYING", "EAGER", "EARLY", "EARTH", "EIGHT", "ELITE", "EMPTY", "ENEMY",
            "ENJOY", "ENTER", "ENTRY", "EQUAL", "ERROR", "EVENT", "EVERY", "EXACT", "EXIST", "EXTRA",
            "FAITH", "FALSE", "FAULT", "FIBER", "FIELD", "FIFTH", "FIFTY", "FIGHT", "FINAL", "FIRST",
            "FIXED", "FLASH", "FLEET", "FLOOR", "FLUID", "FOCUS", "FORCE", "FORTH", "FORTY", "FORUM",
            "FOUND", "FRAME", "FRANK", "FRAUD", "FRESH", "FRONT", "FRUIT", "FULLY", "FUNNY", "GIANT",
            "GIVEN", "GLASS", "GLOBE", "GOING", "GRACE", "GRADE", "GRAND", "GRANT", "GRASS", "GRAVE",
            "GREAT", "GREEN", "GROSS", "GROUP", "GROWN", "GUARD", "GUESS", "GUEST", "GUIDE", "HAPPY",
            "HARRY", "HEART", "HEAVY", "HENCE", "HENRY", "HORSE", "HOTEL", "HOUSE", "HUMAN", "HURRY",
            "IMAGE", "INDEX", "INNER", "INPUT", "ISSUE", "JAPAN", "JIMMY", "JOINT", "JONES", "JUDGE",
            "KNOWN", "LABEL", "LARGE", "LASER", "LATER", "LAUGH", "LAYER", "LEARN", "LEASE", "LEAST",
            "LEAVE", "LEGAL", "LEVEL", "LEWIS", "LIGHT", "LIMIT", "LINKS", "LIVES", "LOCAL", "LOOSE",
            "LOWER", "LUCKY", "LUNCH", "LYING", "MAGIC", "MAJOR", "MAKER", "MARCH", "MARIA", "MATCH",
            "MAYBE", "MAYOR", "MEANT", "MEDIA", "METAL", "MIGHT", "MINOR", "MINUS", "MIXED", "MODEL",
            "MONEY", "MONTH", "MORAL", "MOTOR", "MOUNT", "MOUSE", "MOUTH", "MOVED", "MOVIE", "MUSIC",
            "NEEDS", "NEVER", "NEWLY", "NIGHT", "NOISE", "NORTH", "NOTED", "NOVEL", "NURSE", "OCCUR",
            "OCEAN", "OFFER", "OFTEN", "ORDER", "OTHER", "OUGHT", "PAINT", "PANEL", "PAPER", "PARTY",
            "PEACE", "PETER", "PHASE", "PHONE", "PHOTO", "PIANO", "PICKED", "PIECE", "PILOT", "PITCH",
            "PLACE", "PLAIN", "PLANE", "PLANT", "PLATE", "POINT", "POUND", "POWER", "PRESS", "PRICE",
            "PRIDE", "PRIME", "PRINT", "PRIOR", "PRIZE", "PROOF", "PROUD", "PROVE", "QUEEN", "QUICK",
            "QUIET", "QUITE", "RADIO", "RAISE", "RANGE", "RAPID", "RATIO", "REACH", "READY", "REALM",
            "REBEL", "REFER", "RELAX", "REPAY", "REPLY", "RIGHT", "RIGID", "RIVAL", "RIVER", "ROBIN",
            "ROGER", "ROMAN", "ROUGH", "ROUND", "ROUTE", "ROYAL", "RURAL", "SCALE", "SCENE", "SCOPE",
            "SCORE", "SENSE", "SERVE", "SEVEN", "SHALL", "SHAPE", "SHARE", "SHARP", "SHEET", "SHELF",
            "SHELL", "SHIFT", "SHINE", "SHIRT", "SHOCK", "SHOOT", "SHORT", "SHOWN", "SIGHT", "SILLY",
            "SINCE", "SIXTH", "SIXTY", "SIZED", "SKILL", "SLEEP", "SLIDE", "SMALL", "SMART", "SMILE",
            "SMITH", "SMOKE", "SNAKE", "SNOW", "SOLID", "SOLVE", "SORRY", "SOUND", "SOUTH", "SPACE",
            "SPARE", "SPEAK", "SPEED", "SPEND", "SPENT", "SPLIT", "SPOKE", "SPORT", "STAFF", "STAGE",
            "STAKE", "STAND", "START", "STATE", "STEAM", "STEEL", "STEEP", "STEER", "STICK", "STILL",
            "STOCK", "STONE", "STOOD", "STORE", "STORM", "STORY", "STRIP", "STUCK", "STUDY", "STUFF",
            "STYLE", "SUGAR", "SUITE", "SUPER", "SWEET", "TABLE", "TAKEN", "TASTE", "TAXES", "TEACH",
            "TEAM", "TEETH", "TERRY", "TEXAS", "THANK", "THEFT", "THEIR", "THEME", "THERE", "THESE",
            "THICK", "THING", "THINK", "THIRD", "THOSE", "THREE", "THREW", "THROW", "THUMB", "TIGHT",
            "TIRED", "TITLE", "TODAY", "TOPIC", "TOTAL", "TOUCH", "TOUGH", "TOWER", "TRACK", "TRADE",
            "TRAIN", "TREAT", "TREND", "TRIAL", "TRIBE", "TRICK", "TRIED", "TRIES", "TRUCK", "TRULY",
            "TRUNK", "TRUST", "TRUTH", "TWICE", "TWIST", "TYLER", "UNCLE", "UNDER", "UNDUE", "UNION",
            "UNITY", "UNTIL", "UPPER", "UPSET", "URBAN", "USAGE", "USUAL", "VALID", "VALUE", "VIDEO",
            "VIRUS", "VISIT", "VITAL", "VOCAL", "VOICE", "WASTE", "WATCH", "WATER", "WHEEL", "WHERE",
            "WHICH", "WHILE", "WHITE", "WHOLE", "WHOSE", "WOMAN", "WOMEN", "WORLD", "WORRY", "WORSE",
            "WORST", "WORTH", "WOULD", "WRITE", "WRONG", "WROTE", "YOUNG", "YOUTH"
        ]

    def load_answer_words(self):
        """从 wordle_words.txt 加载题库（答案词汇）"""
        answer_words = set()

        # 从题库文件加载答案词汇
        word_file_path = "wordle_words.txt"
        if os.path.exists(word_file_path):
            try:
                with open(word_file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        word = line.strip().upper()
                        if len(word) == 5 and word.isalpha():
                            answer_words.add(word)
                print(f"从 {word_file_path} 加载了 {len(answer_words)} 个题库单词")
            except Exception as e:
                print(f"加载题库文件失败: {e}")

        # 如果文件不存在或加载失败，使用内置单词列表作为备选
        if not answer_words:
            answer_words = set(self.word_list)
            print(f"使用内置题库单词列表，共 {len(answer_words)} 个单词")

        return answer_words

    def load_valid_words(self):
        """从 valid-wordle-words.txt 加载有效猜测词汇"""
        valid_words = set()

        # 从有效猜测词汇文件加载
        valid_file_path = "valid-wordle-words.txt"
        if os.path.exists(valid_file_path):
            try:
                with open(valid_file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        word = line.strip().upper()
                        if len(word) == 5 and word.isalpha():
                            valid_words.add(word)
                print(f"从 {valid_file_path} 加载了 {len(valid_words)} 个有效猜测单词")
            except Exception as e:
                print(f"加载有效猜测词汇文件失败: {e}")

        # 如果文件不存在或加载失败，使用题库词汇作为备选
        if not valid_words:
            valid_words = self.answer_words.copy()
            print(f"使用题库作为有效猜测词汇，共 {len(valid_words)} 个单词")

        return valid_words

    def start_new_game(self):
        """开始新游戏"""
        # 从题库中随机选择一个单词作为答案
        if self.answer_words:
            self.target_word = random.choice(list(self.answer_words))
        else:
            # 备选方案：使用内置单词列表
            self.target_word = random.choice(self.word_list)

        self.guesses = []
        self.guessed_words = set()  # 重置已猜测单词列表
        self.game_active = True
        self.start_time = time.time()
        return self.target_word
    
    def is_valid_word(self, word):
        """验证单词是否有效"""
        if len(word) != self.word_length:
            return False

        # 首先检查是否为纯字母
        if not word.isalpha():
            return False

        word = word.upper()

        # 直接使用本地词汇文件验证
        return word in self.valid_words
    
    def make_guess(self, word):
        """进行猜测"""
        if not self.game_active:
            return False, "游戏未开始"

        if len(self.guesses) >= self.max_guesses:
            return False, "已达到最大猜测次数"

        word = word.upper().strip()

        if not self.is_valid_word(word):
            return False, f"{word} 不是一个合法的单词"

        # 检查是否已经猜过这个单词
        if word in self.guessed_words:
            return False, f"{word} 已经猜过了"

        # 添加到已猜测单词列表
        self.guessed_words.add(word)

        # 计算结果
        result = self.calculate_result(word)
        self.guesses.append({"word": word, "result": result})

        # 检查是否获胜
        if word == self.target_word:
            self.game_active = False
            return True, "恭喜！猜对了！"

        # 检查是否失败
        if len(self.guesses) >= self.max_guesses:
            self.game_active = False
            return True, f"游戏结束！答案是 {self.target_word}"

        return True, "继续猜测"
    
    def calculate_result(self, guess):
        """计算猜测结果"""
        result = []
        target = list(self.target_word)
        guess_list = list(guess)
        
        # 第一遍：标记完全匹配的字母
        for i in range(len(guess_list)):
            if guess_list[i] == target[i]:
                result.append("correct")  # 绿色 - 正确位置
                target[i] = None  # 标记为已使用
                guess_list[i] = None
            else:
                result.append(None)
        
        # 第二遍：标记存在但位置错误的字母
        for i in range(len(guess_list)):
            if guess_list[i] is not None:
                if guess_list[i] in target:
                    result[i] = "present"  # 黄色 - 存在但位置错误
                    target[target.index(guess_list[i])] = None  # 标记为已使用
                else:
                    result[i] = "absent"  # 灰色 - 不存在
        
        return result
    
    def format_guess_display(self, guess_data):
        """格式化猜测结果显示"""
        word = guess_data["word"]
        result = guess_data["result"]
        
        formatted = ""
        for i, letter in enumerate(word):
            if result[i] == "correct":
                formatted += f"**{letter}**"  # 绿色 - 粗体
            elif result[i] == "present":
                formatted += letter  # 黄色 - 普通文本
            else:
                formatted += f"~~{letter}~~"  # 灰色 - 删除线
        
        return formatted
    
    def get_game_display(self):
        """获取游戏显示"""
        display_lines = []
        
        # 显示已猜测的单词
        for guess_data in self.guesses:
            display_lines.append(self.format_guess_display(guess_data))
        
        # 显示剩余的空行
        remaining = self.max_guesses - len(self.guesses)
        for _ in range(remaining):
            display_lines.append("-----")
        
        return "\n".join(display_lines)
    
    def get_game_status(self):
        """获取游戏状态"""
        if not self.game_active and len(self.guesses) == 0:
            return "游戏未开始"
        
        status = f"Wordle 游戏 ({len(self.guesses)}/{self.max_guesses})"
        
        if not self.game_active:
            if len(self.guesses) > 0 and self.guesses[-1]["word"] == self.target_word:
                elapsed = int(time.time() - self.start_time) if self.start_time else 0
                status += f" - 🎉 获胜！用时 {elapsed} 秒"
            else:
                status += f" - 💀 失败！答案: {self.target_word}"
        else:
            status += " - 🎮 进行中"
        
        return status
