{"main": {"title": "🤖 BoB 机器人帮助信息", "sections": {"💬 AI 功能": ["@BoB <消息> - 与 AI 对话", "draw <描述> - 使用 Flux 模型绘画"], "📝 实用功能": ["msg <用户名> <留言内容> - 给用户留言", "history [数量] - 查看聊天历史", "delete - 删除个人 AI 聊天记录"], "🎮 游戏": ["UNO - 经典卡牌游戏 (输入 'help uno' 查看详情)", "Wordle - 单词猜测游戏 (输入 'help wordle' 查看详情)"], "💡 其他": ["help <功能名> - 查看具体功能帮助", "私聊机器人可进行 AI 对话"]}}, "uno": {"title": "🎮 UNO 游戏帮助", "sections": {"🎯 游戏规则": ["• 经典UNO卡牌游戏，2-8人参与", "• 每人7张牌，先出完牌的玩家获胜", "• 出牌必须匹配颜色或数字", "• 剩1张牌时必须喊UNO，否则摸2张惩罚"], "🃏 卡牌类型": ["• 数字牌: 红0-9, 蓝0-9, 绿0-9, 黄0-9", "• 功能牌: 跳过, 反转, +2", "• 万能牌: 变色, +4变色"], "📝 游戏命令": ["uno join - 加入游戏", "uno start - 开始游戏(需2+人)", "uno play <牌> [颜色] - 出牌", "uno draw - 摸牌", "uno uno - 喊UNO", "uno hand - 查看手牌", "uno status - 查看游戏状态", "uno leave - 离开游戏", "uno reset - 重置游戏"], "💡 出牌示例": ["uno play 红5 - 出红色5", "uno play 跳过 - 出跳过牌", "uno play 变色 蓝 - 出变色牌并指定蓝色", "uno play +4变色 绿 - 出+4变色牌并指定绿色"]}}, "wordle": {"title": "🎯 Wordle 游戏帮助", "sections": {"🎮 游戏规则": ["• 猜一个5字母的英文单词", "• 有6次猜测机会", "• 任何人都可以参与猜测，无需加入", "• 多人协作，共同猜出答案"], "🎨 颜色含义": ["• **粗体** = 字母正确且位置正确 (绿色)", "• 普通文本 = 字母存在但位置错误 (黄色)", "• ~~删除线~~ = 字母不存在 (灰色)"], "📝 游戏命令": ["wordle start - 开始新游戏", "wordle status - 查看游戏状态", "wordle help - 显示游戏帮助"], "💡 游戏技巧": ["• 直接输入5字母单词进行猜测", "• 建议先猜常见元音字母 (A,<PERSON>,<PERSON>,O,U)", "• 利用黄色提示调整字母位置", "• 避免重复使用灰色字母"]}}, "ai": {"title": "💬 AI 对话帮助", "sections": {"🤖 基本用法": ["@BoB <消息> - 公开与AI对话", "私聊机器人 - 私密AI对话"], "🎨 绘画功能": ["draw <描述> - 使用Flux模型绘画", "支持中英文描述", "例: draw 一只可爱的小猫"], "💾 数据管理": ["delete - 删除个人AI聊天记录", "每个用户的对话历史独立保存", "最多保存50条对话记录"]}}, "message": {"title": "📝 留言系统帮助", "sections": {"💌 基本用法": ["msg <用户名> <留言内容> - 给用户留言", "例: msg <PERSON> 明天记得开会"], "📬 留言特点": ["• 用户进入聊天室时自动收到留言", "• 留言通过私聊发送，保护隐私", "• 留言发送后自动删除", "• 支持给同一用户发送多条留言"], "⏰ 留言格式": ["留言包含发送时间和发送者信息", "格式: [时间] 发送者: 留言内容"]}}, "history": {"title": "📚 历史记录帮助", "sections": {"📖 基本用法": ["history - 查看最近50条聊天记录", "history <数量> - 查看指定数量的记录", "例: history 100"], "💾 存储格式": ["• 使用JSON Lines格式存储", "• 包含时间戳、用户名、消息内容", "• 按日期分文件存储", "• 支持旧格式兼容"], "🔒 隐私保护": ["• 历史记录通过私聊发送", "• 最多显示200条记录", "• 过长消息自动分块发送"]}}}